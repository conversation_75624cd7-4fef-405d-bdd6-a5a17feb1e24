import os
import numpy as np
import lasio as las
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score

# defining a function that extract LAS files from a folder

def load_las_files(folder_path):
    wells = []
    for filename in os.listdir(folder_path):
        if filename.endswith(".las"):
            las_path = os.path.join(folder_path, filename)
            well = las.read(las_path)
            wells.append(well)
    return wells

folder_path = '/kaggle/input/logging-data'

# Call the function with the folder path
wells = load_las_files(folder_path)

print(f"Loaded {len(wells)} LAS files.")

for well in wells:
    print(well.keys())

data = []
for well in wells:
    df = well.df()  # Convert LAS data to a DataFrame

    # Ensure DEPT is not used as the index
    if df.index.name == 'DEPT':
        df = df.reset_index()

    # Ensure consistent data type for DEPT
    df['DEPT'] = df['DEPT'].astype(float)

    data.append(df)

# Concatenate all DataFrames
data = pd.concat(data, axis=0, ignore_index=True)

print(data.info())
print(data.head())

data.isnull().sum()

# remove the nulls
data.dropna(inplace=True)

# making LITH as a int
data['LITH']=data['LITH'].astype(int)

data

#  Gamma Ray vs. Porosity

plt.figure(figsize=(10, 6))
sns.scatterplot(x='GAMMA', y='POROSITY', hue='LITH', data=data)
plt.title('Gamma Ray vs. Porosity')
plt.xlabel('Gamma Ray (API)')
plt.ylabel('Porosity')
plt.show()

# Porosity vs Permeability

plt.figure(figsize=(10, 6))
sns.scatterplot(x='POROSITY', y='PERM', hue='LITH', data=data)
plt.title('Porosity vs Permeability')
plt.xlabel('Porosity')
plt.ylabel('Permeability')
plt.show()

# getting LITH count to determine if there is Imbalance in the data
lith_counts = data['LITH'].value_counts()
lith_counts

# plotting it
plt.figure(figsize=(10, 6))
sns.barplot(x=lith_counts.index, y=lith_counts.values,palette='colorblind')
plt.title('LITH Count')
plt.xlabel('LITH')
plt.ylabel('Count')
plt.show()

# Separate features and target
X = data.drop(columns=['LITH'])  # Features
y = data['LITH']  # Target

print(X) # Checking for data leakage

# Split the data into training and testing sets (e.g., 80% training, 20% testing)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Feature Scaling
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

rf_model=RandomForestClassifier(random_state=42)
rf_model.fit(X_train_scaled, y_train)

y_pred = rf_model.predict(X_test_scaled)

class_report = classification_report(y_test, y_pred)
print("Classification Report:")
print(class_report)

#confustion matrix

sns.heatmap(confusion_matrix(y_test, y_pred), annot=True, fmt='d', cmap='Blues')
plt.title('SVM Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('True')
plt.show()

# #Observations:
# the model acheived 100% accuracy on the test set
# This is highly unusual for real-world datasets and suggests there might be an issue with the data or the model's evaluation process.
# Let's try using an SVM (Support Vector Machine) to see if the issue persists. If the SVM also achieves 100% accuracy, it strongly indicates a problem with the dataset.

# Split the data into training and testing sets (e.g., 80% training, 20% testing)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Feature Scaling
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Train SVM model
svm_model = SVC(kernel='rbf', random_state=42)
svm_model.fit(X_train_scaled, y_train)

# Evaluate SVM model
y_pred_svm = svm_model.predict(X_test_scaled)
print("SVM Classification Report:\n", classification_report(y_test, y_pred_svm))
print("SVM Confusion Matrix:\n", confusion_matrix(y_test, y_pred_svm))

#confusion matrix
sns.heatmap(confusion_matrix(y_test, y_pred_svm), annot=True, fmt='d', cmap='Blues')
plt.title('SVM Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('True')
plt.show()

# Observations:
# Random Forest model achieving 100% accuracy while SVM achieves around 98% strongly suggests one or more of the following issues: ##### 1- Overfitting in Random Forest ##### 2- Feature Dominance ##### 3- Data Leakage ##### 4- Dataset Might Be Too Easy
# First let's check feature importance and correlation to determine which feature is dominent.

# Feature importance
importances = rf_model.feature_importances_
feature_names = X.columns
feature_importance_df = pd.DataFrame({'Feature': feature_names, 'Importance': importances})
print(feature_importance_df.sort_values(by='Importance', ascending=False))

# plotting it for better visual

sns.barplot(x='Importance', y='Feature', data=feature_importance_df.sort_values(by='Importance', ascending=False))
plt.title('Feature Importance')
plt.show()

# data correlation
corr_matrix = data.corr()
plt.figure(figsize=(10, 6))
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt=".2f")
plt.title('Correlation Matrix')
plt.show()

# Observaion
# It seem evident that the Porosity feature is dominant
# and there's high correlation between porosity and the target (Lithology)
# Let's try ommitting the porosity and check model performance.

X_reduced = X.drop(columns=['POROSITY'])

X_train_reduced, X_test_reduced, y_train, y_test = train_test_split(X_reduced, y, test_size=0.2, random_state=42, shuffle=True)
 # made sure the data is shuffled to avoid data memorization


# Feature Scaling
scaler = StandardScaler()
X_train_scaled1 = scaler.fit_transform(X_train_reduced)
X_test_scaled1 = scaler.transform(X_test_reduced)


SVM_model_reduced = SVC(kernel='rbf',random_state=42, class_weight='balanced')
SVM_model_reduced.fit(X_train_scaled1, y_train)

SVM_y_pred_reduced = SVM_model_reduced.predict(X_test_scaled1)

print("Reduced Feature RF Classification Report:\n", classification_report(y_test, SVM_y_pred_reduced))

#confusion matrix
sns.heatmap(confusion_matrix(y_test, SVM_y_pred_reduced), annot=True, fmt='d', cmap='Blues')
plt.title('SVM Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('True')
plt.show()

# Observations:
# As it seems the model acheived good results on class 1 and 2, this may due to the inbalance mentioned earlier that was not addressed, or due to features not scaled.
# As shown above the model had improved.
# BUT It's facing some issues with Class 0 persicion and Class 2 recall.
# that's indicating that omitting Porosity had a negative impact on the model.