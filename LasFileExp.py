import laspy
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# Load LiDAR LAS file
las_file_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\ONGC\\Project1\\archive (3)\\1_segmented.las"
print(f"Loading LAS file: {las_file_path}")
las = laspy.read(las_file_path)

# Display basic file information
print("LAS File Information:")
print(f"File format: {las.header.version}")
print(f"Point format: {las.header.point_format}")
print(f"Number of points: {len(las.points)}")
# Load LiDAR LAS file
las_file_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\ONGC\\Project1\\archive (3)\\1_segmented.las"
print(f"Loading LAS file: {las_file_path}")
las = laspy.read(las_file_path)