import laspy
import pandas as pd

# Load LiDAR LAS file
las_file_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\ONGC\\Project1\\archive (3)\\1_segmented.las"
print(f"Loading LAS file: {las_file_path}")
las = laspy.read(las_file_path)

# Display basic file information
print("LAS File Information:")
print(f"File format: {las.header.version}")
print(f"Point format: {las.header.point_format}")
print(f"Number of points: {len(las.points)}")
print(f"Scale factors: X={las.header.x_scale}, Y={las.header.y_scale}, Z={las.header.z_scale}")
print(f"Offset: X={las.header.x_offset}, Y={las.header.y_offset}, Z={las.header.z_offset}")

# Display coordinate bounds
print(f"\nCoordinate bounds:")
print(f"X: {las.header.x_min} to {las.header.x_max}")
print(f"Y: {las.header.y_min} to {las.header.y_max}")
print(f"Z: {las.header.z_min} to {las.header.z_max}")

# Display available point dimensions
print(f"\nAvailable point dimensions: {list(las.point_format.dimension_names)}")

# Convert point data to a DataFrame (first 1000 points for preview)
num_preview_points = min(1000, len(las.points))

# Create DataFrame with individual point values
points_data = {
    'X': las.x[:num_preview_points].copy(),
    'Y': las.y[:num_preview_points].copy(),
    'Z': las.z[:num_preview_points].copy()
}

# Add other available dimensions if they exist
if hasattr(las, 'intensity') and len(las.intensity) > 0:
    points_data['Intensity'] = las.intensity[:num_preview_points].copy()
if hasattr(las, 'classification') and len(las.classification) > 0:
    points_data['Classification'] = las.classification[:num_preview_points].copy()
if hasattr(las, 'return_number') and len(las.return_number) > 0:
    points_data['Return_Number'] = las.return_number[:num_preview_points].copy()

df = pd.DataFrame(points_data)
print(f"\nFirst 10 points preview:")
print(df.head(10))

# Display some basic statistics
print(f"\nBasic statistics for the first {num_preview_points} points:")
print(df.describe())
