import pandas as pd
import matplotlib.pyplot as plt
import lasio as ls

las = ls.read("C:/Users/<USER>/OneDrive/Desktop/ONGC/Dataset/L0509_comp.las") # type: ignore

las.sections.keys() # type: ignore

las.sections['Version']

for item in las.sections['Well']:
    print(f"{item.descr} ({item.mnemonic}): \t\t {item.value}")

las.sections['Well']['CTRY']= 'U.S'

for item in las.sections['Well']:
    print(f"{item.descr} ({item.mnemonic}): \t\t {item.value}")

las.well.WELL.value

for curve in las.curves:
    print(curve.mnemonic)

for count, curve in enumerate(las.curves):
    print(f"Curve: {curve.mnemonic}, \t Units: {curve.unit}, \t Description: {curve.descr}")
print(f"There are a total of: {count+1} curves present within this file")

well = las.df()

well.head()

well.describe()

well.info()

well.plot()

well.plot(y='RHOB')

well.plot(y='DRHO')

well.plot(y='NPHI')

well.plot(y='GR')

well.plot(y='DT')


well.plot(y='SP')